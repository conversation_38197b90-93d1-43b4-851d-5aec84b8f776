import pymem
import pymem.process
import time
import customtkinter as ctk
import threading
import sys

# --- 配置区 ---
PROCESS_NAME = "TheSpellBrigade.exe"
# ----------------------------------------

# 设置外观模式和颜色主题
ctk.set_appearance_mode("dark")  # 模式: "System" (标准), "Dark", "Light"
ctk.set_default_color_theme("blue")  # 主题: "blue" (标准), "green", "dark-blue"

def find_pointer_address(pm, base, offsets):
    """
    根据基址和多级偏移计算最终地址
    """
    try:
        # 在64位游戏中，指针是8字节 (longlong)
        addr = pm.read_longlong(base)
        
        # 遍历偏移量列表，除了最后一个
        for offset in offsets[:-1]:
            # 如果地址为空，说明指针链断了
            if addr == 0:
                return None
            addr = pm.read_longlong(addr + offset)
            
        # 加上最后一个偏移量，得到最终地址
        if addr == 0:
            return None
        return addr + offsets[-1]
    except Exception:
        # 任何读取错误都意味着指针无效
        return None

class SpellBrigadeCheatCTK(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # 配置窗口
        self.title("The Spell Brigade 金币修改器")
        self.geometry("650x550")
        self.resizable(True, True)
        
        # 初始化变量
        self.pm = None
        self.is_connected = False
        self.monitoring = False
        self.current_gold = 0
        self.gold_address = None
        
        # 配置
        self.static_base_offset = 0x3858F00
        self.offsets = [0x20, 0xC0, 0x10, 0xB8, 0x0, 0x38]
        
        # 创建UI
        self.setup_ui()
        
    def setup_ui(self):
        """创建用户界面"""
        # 主标题
        self.title_label = ctk.CTkLabel(self, text="The Spell Brigade 金币修改器", 
                                       font=ctk.CTkFont(size=24, weight="bold"))
        self.title_label.pack(pady=20)
        
        # 连接状态框架
        self.status_frame = ctk.CTkFrame(self)
        self.status_frame.pack(pady=10, padx=20, fill="x")
        
        self.status_label = ctk.CTkLabel(self.status_frame, text="游戏连接状态: ", 
                                        font=ctk.CTkFont(size=14))
        self.status_label.pack(side="left", padx=10, pady=10)
        
        self.status_indicator = ctk.CTkLabel(self.status_frame, text="未连接", 
                                           font=ctk.CTkFont(size=14, weight="bold"),
                                           text_color="red")
        self.status_indicator.pack(side="left", padx=(0, 10), pady=10)
        
        # 连接按钮
        self.connect_btn = ctk.CTkButton(self, text="连接游戏", command=self.connect_game,
                                        font=ctk.CTkFont(size=14, weight="bold"),
                                        height=35, width=120)
        self.connect_btn.pack(pady=10)
        
        # 当前金币显示框架
        self.gold_frame = ctk.CTkFrame(self)
        self.gold_frame.pack(pady=10, padx=20, fill="x")
        
        self.gold_label = ctk.CTkLabel(self.gold_frame, text="当前金币: ", 
                                      font=ctk.CTkFont(size=16))
        self.gold_label.pack(side="left", padx=10, pady=15)
        
        self.gold_display = ctk.CTkLabel(self.gold_frame, text="0", 
                                        font=ctk.CTkFont(size=16, weight="bold"),
                                        text_color="yellow")
        self.gold_display.pack(side="left", padx=(0, 10), pady=15)
        
        # 修改金币框架
        self.modify_frame = ctk.CTkFrame(self)
        self.modify_frame.pack(pady=15, padx=20, fill="x")
        
        self.modify_label = ctk.CTkLabel(self.modify_frame, text="修改金币数量:", 
                                        font=ctk.CTkFont(size=14))
        self.modify_label.pack(anchor="w", padx=15, pady=(15, 5))
        
        self.input_frame = ctk.CTkFrame(self.modify_frame, fg_color="transparent")
        self.input_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        self.gold_entry = ctk.CTkEntry(self.input_frame, placeholder_text="输入金币数量",
                                      font=ctk.CTkFont(size=14), width=200)
        self.gold_entry.pack(side="left", padx=(0, 10))
        
        self.modify_btn = ctk.CTkButton(self.input_frame, text="修改金币", 
                                       command=self.modify_gold,
                                       font=ctk.CTkFont(size=14, weight="bold"),
                                       height=32, width=100, state="disabled")
        self.modify_btn.pack(side="left")
        
        # 快捷金币按钮框架
        self.quick_frame = ctk.CTkFrame(self)
        self.quick_frame.pack(pady=10, padx=20, fill="x")
        
        self.quick_label = ctk.CTkLabel(self.quick_frame, text="快捷设置:", 
                                       font=ctk.CTkFont(size=14))
        self.quick_label.pack(anchor="w", padx=15, pady=(15, 5))
        
        self.buttons_frame = ctk.CTkFrame(self.quick_frame, fg_color="transparent")
        self.buttons_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        # 创建快捷按钮
        self.quick_buttons = []
        quick_amounts = [1000, 5000, 10000, 50000, 100000, 999999]
        for i, amount in enumerate(quick_amounts):
            btn = ctk.CTkButton(self.buttons_frame, text=f"{amount:,}", 
                               command=lambda a=amount: self.set_quick_amount(a),
                               font=ctk.CTkFont(size=12), height=28, width=80,
                               state="disabled")
            btn.grid(row=0, column=i, padx=3, pady=5)
            self.quick_buttons.append(btn)
        
        # 日志显示区域
        self.log_frame = ctk.CTkFrame(self)
        self.log_frame.pack(pady=15, padx=20, fill="both", expand=True)
        
        self.log_label = ctk.CTkLabel(self.log_frame, text="操作日志:", 
                                     font=ctk.CTkFont(size=14))
        self.log_label.pack(anchor="w", padx=15, pady=(15, 5))
        
        self.log_text = ctk.CTkTextbox(self.log_frame, height=150, 
                                      font=ctk.CTkFont(family="Consolas", size=11))
        self.log_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # 初始日志
        self.log("程序启动完成")
        self.log("提示：请先启动游戏，然后点击'连接游戏'按钮")
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert("end", log_message)
        self.log_text.see("end")
        
    def connect_game(self):
        """连接到游戏进程"""
        if self.is_connected:
            self.disconnect_game()
            return
            
        try:
            self.log(f"正在查找游戏进程: {PROCESS_NAME}...")
            self.pm = pymem.Pymem(PROCESS_NAME)
            self.log(f"成功附加到进程！进程ID: {self.pm.process_id}")
            
            # 获取模块基址
            module_base = pymem.process.module_from_name(self.pm.process_handle, "GameAssembly.dll").lpBaseOfDll
            self.log(f"GameAssembly.dll 模块基址: {hex(module_base)}")
            self.module_base = module_base
            
            self.is_connected = True
            self.status_indicator.configure(text="已连接", text_color="green")
            self.connect_btn.configure(text="断开连接")
            self.modify_btn.configure(state="normal")
            
            # 启用快捷按钮
            for btn in self.quick_buttons:
                btn.configure(state="normal")
            
            # 开始监控金币
            self.start_monitoring()
            
        except pymem.exception.ProcessNotFound:
            self.log(f"错误: 未找到游戏进程 '{PROCESS_NAME}'")
            self.log("请先启动游戏，然后再尝试连接")
        except Exception as e:
            self.log(f"连接失败: {e}")
            
    def disconnect_game(self):
        """断开游戏连接"""
        self.monitoring = False
        self.is_connected = False
        self.pm = None
        
        self.status_indicator.configure(text="未连接", text_color="red")
        self.connect_btn.configure(text="连接游戏")
        self.modify_btn.configure(state="disabled")
        self.gold_display.configure(text="0")
        
        # 禁用快捷按钮
        for btn in self.quick_buttons:
            btn.configure(state="disabled")
            
        self.log("已断开游戏连接")
        
    def start_monitoring(self):
        """开始监控金币"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_gold, daemon=True)
        self.monitor_thread.start()
        
    def monitor_gold(self):
        """监控金币数量"""
        while self.monitoring and self.is_connected:
            try:
                gold_address = find_pointer_address(self.pm, self.module_base + self.static_base_offset, self.offsets)
                
                if gold_address:
                    current_gold = self.pm.read_int(gold_address)
                    self.current_gold = current_gold
                    self.gold_address = gold_address
                    
                    # 更新UI（需要在主线程中执行）
                    self.after(0, lambda: self.gold_display.configure(text=f"{current_gold:,}"))
                else:
                    self.after(0, lambda: self.gold_display.configure(text="未检测到"))
                    
            except Exception as e:
                self.after(0, lambda: self.log(f"监控错误: {e}"))
                break
                
            time.sleep(0.5)  # 每0.5秒检查一次
            
    def set_quick_amount(self, amount):
        """设置快捷金币数量"""
        self.gold_entry.delete(0, "end")
        self.gold_entry.insert(0, str(amount))
        
    def modify_gold(self):
        """修改金币"""
        if not self.is_connected or not self.gold_address:
            self.log("错误: 请先连接游戏并等待检测到金币地址")
            return
            
        try:
            new_gold_str = self.gold_entry.get()
            if not new_gold_str:
                self.log("错误: 请输入金币数量")
                return
                
            new_gold = int(new_gold_str)
            if new_gold < 0:
                self.log("错误: 金币数量不能为负数")
                return
                
            # 修改金币
            self.pm.write_int(self.gold_address, new_gold)
            
            # 验证修改
            time.sleep(0.1)
            actual_gold = self.pm.read_int(self.gold_address)
            
            if actual_gold == new_gold:
                self.log(f"成功！金币已修改为: {new_gold:,}")
            else:
                self.log("写入失败！可能被游戏保护机制阻止")
                
        except ValueError:
            self.log("错误: 请输入有效的数字")
        except Exception as e:
            self.log(f"修改金币时发生错误: {e}")
            
    def on_closing(self):
        """窗口关闭事件"""
        if self.is_connected:
            self.disconnect_game()
        self.destroy()

def main():
    """主程序入口"""
    app = SpellBrigadeCheatCTK()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop()

if __name__ == "__main__":
    main()
