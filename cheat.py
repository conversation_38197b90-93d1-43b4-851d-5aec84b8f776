import pymem
import pymem.process
import time
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import sys

# --- 配置区 (已根据你的信息填写完毕) ---
PROCESS_NAME = "TheSpellBrigade.exe"
# ----------------------------------------

def find_pointer_address(pm, base, offsets):
    """
    根据基址和多级偏移计算最终地址
    """
    try:
        # 在64位游戏中，指针是8字节 (longlong)
        addr = pm.read_longlong(base)
        
        # 遍历偏移量列表，除了最后一个
        for offset in offsets[:-1]:
            # 如果地址为空，说明指针链断了
            if addr == 0:
                return None
            addr = pm.read_longlong(addr + offset)
            
        # 加上最后一个偏移量，得到最终地址
        if addr == 0:
            return None
        return addr + offsets[-1]
    except Exception:
        # 任何读取错误都意味着指针无效
        return None

class SpellBrigadeCheatGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("The Spell Brigade 金币修改器")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # 设置窗口图标和样式
        self.root.configure(bg='#2b2b2b')

        # 初始化变量
        self.pm = None
        self.is_connected = False
        self.monitoring = False
        self.current_gold = 0
        self.gold_address = None

        # 配置
        self.static_base_offset = 0x3858F00
        self.offsets = [0x20, 0xC0, 0x10, 0xB8, 0x0, 0x38]

        self.setup_ui()

    def setup_ui(self):
        # 主标题
        title_label = tk.Label(self.root, text="The Spell Brigade 金币修改器",
                              font=("Arial", 16, "bold"), fg='#ffffff', bg='#2b2b2b')
        title_label.pack(pady=10)

        # 连接状态框架
        status_frame = tk.Frame(self.root, bg='#2b2b2b')
        status_frame.pack(pady=5, padx=20, fill='x')

        tk.Label(status_frame, text="游戏连接状态:", font=("Arial", 10),
                fg='#ffffff', bg='#2b2b2b').pack(side='left')

        self.status_label = tk.Label(status_frame, text="未连接", font=("Arial", 10, "bold"),
                                   fg='#ff4444', bg='#2b2b2b')
        self.status_label.pack(side='left', padx=(10, 0))

        # 连接按钮
        self.connect_btn = tk.Button(self.root, text="连接游戏", command=self.connect_game,
                                   bg='#4CAF50', fg='white', font=("Arial", 10, "bold"),
                                   relief='flat', padx=20, pady=5)
        self.connect_btn.pack(pady=10)

        # 当前金币显示
        gold_frame = tk.Frame(self.root, bg='#2b2b2b')
        gold_frame.pack(pady=10, padx=20, fill='x')

        tk.Label(gold_frame, text="当前金币:", font=("Arial", 12),
                fg='#ffffff', bg='#2b2b2b').pack(side='left')

        self.gold_label = tk.Label(gold_frame, text="0", font=("Arial", 12, "bold"),
                                 fg='#ffdd44', bg='#2b2b2b')
        self.gold_label.pack(side='left', padx=(10, 0))

        # 修改金币框架
        modify_frame = tk.Frame(self.root, bg='#2b2b2b')
        modify_frame.pack(pady=20, padx=20, fill='x')

        tk.Label(modify_frame, text="修改金币数量:", font=("Arial", 10),
                fg='#ffffff', bg='#2b2b2b').pack(anchor='w')

        input_frame = tk.Frame(modify_frame, bg='#2b2b2b')
        input_frame.pack(fill='x', pady=(5, 0))

        self.gold_entry = tk.Entry(input_frame, font=("Arial", 12), width=15)
        self.gold_entry.pack(side='left')

        self.modify_btn = tk.Button(input_frame, text="修改金币", command=self.modify_gold,
                                  bg='#FF9800', fg='white', font=("Arial", 10, "bold"),
                                  relief='flat', padx=15, pady=5, state='disabled')
        self.modify_btn.pack(side='left', padx=(10, 0))

        # 快捷金币按钮
        quick_frame = tk.Frame(self.root, bg='#2b2b2b')
        quick_frame.pack(pady=10, padx=20, fill='x')

        tk.Label(quick_frame, text="快捷设置:", font=("Arial", 10),
                fg='#ffffff', bg='#2b2b2b').pack(anchor='w')

        buttons_frame = tk.Frame(quick_frame, bg='#2b2b2b')
        buttons_frame.pack(fill='x', pady=(5, 0))

        quick_amounts = [1000, 5000, 10000, 50000, 100000, 999999]
        for amount in quick_amounts:
            btn = tk.Button(buttons_frame, text=f"{amount:,}",
                          command=lambda a=amount: self.set_quick_amount(a),
                          bg='#2196F3', fg='white', font=("Arial", 9),
                          relief='flat', padx=10, pady=3, state='disabled')
            btn.pack(side='left', padx=2)
            # 保存按钮引用以便后续启用/禁用
            if not hasattr(self, 'quick_buttons'):
                self.quick_buttons = []
            self.quick_buttons.append(btn)

        # 日志显示区域
        log_frame = tk.Frame(self.root, bg='#2b2b2b')
        log_frame.pack(pady=10, padx=20, fill='both', expand=True)

        tk.Label(log_frame, text="操作日志:", font=("Arial", 10),
                fg='#ffffff', bg='#2b2b2b').pack(anchor='w')

        self.log_text = scrolledtext.ScrolledText(log_frame, height=8,
                                                bg='#1e1e1e', fg='#ffffff',
                                                font=("Consolas", 9))
        self.log_text.pack(fill='both', expand=True, pady=(5, 0))

        # 初始日志
        self.log("程序启动完成")
        self.log("提示：请先启动游戏，然后点击'连接游戏'按钮")

    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def connect_game(self):
        """连接到游戏进程"""
        if self.is_connected:
            self.disconnect_game()
            return

        try:
            self.log(f"正在查找游戏进程: {PROCESS_NAME}...")
            self.pm = pymem.Pymem(PROCESS_NAME)
            self.log(f"成功附加到进程！进程ID: {self.pm.process_id}")

            # 获取模块基址
            module_base = pymem.process.module_from_name(self.pm.process_handle, "GameAssembly.dll").lpBaseOfDll
            self.log(f"GameAssembly.dll 模块基址: {hex(module_base)}")
            self.module_base = module_base

            self.is_connected = True
            self.status_label.config(text="已连接", fg='#44ff44')
            self.connect_btn.config(text="断开连接", bg='#f44336')
            self.modify_btn.config(state='normal')

            # 启用快捷按钮
            for btn in self.quick_buttons:
                btn.config(state='normal')

            # 开始监控金币
            self.start_monitoring()

        except pymem.exception.ProcessNotFound:
            self.log(f"错误: 未找到游戏进程 '{PROCESS_NAME}'")
            self.log("请先启动游戏，然后再尝试连接")
            messagebox.showerror("连接失败", f"未找到游戏进程 '{PROCESS_NAME}'\n请先启动游戏！")
        except Exception as e:
            self.log(f"连接失败: {e}")
            messagebox.showerror("连接失败", f"连接游戏时发生错误:\n{e}")

    def disconnect_game(self):
        """断开游戏连接"""
        self.monitoring = False
        self.is_connected = False
        self.pm = None

        self.status_label.config(text="未连接", fg='#ff4444')
        self.connect_btn.config(text="连接游戏", bg='#4CAF50')
        self.modify_btn.config(state='disabled')
        self.gold_label.config(text="0")

        # 禁用快捷按钮
        for btn in self.quick_buttons:
            btn.config(state='disabled')

        self.log("已断开游戏连接")

    def start_monitoring(self):
        """开始监控金币"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_gold, daemon=True)
        self.monitor_thread.start()

    def monitor_gold(self):
        """监控金币数量"""
        while self.monitoring and self.is_connected:
            try:
                gold_address = find_pointer_address(self.pm, self.module_base + self.static_base_offset, self.offsets)

                if gold_address:
                    current_gold = self.pm.read_int(gold_address)
                    self.current_gold = current_gold
                    self.gold_address = gold_address

                    # 更新UI（需要在主线程中执行）
                    self.root.after(0, lambda: self.gold_label.config(text=f"{current_gold:,}"))
                else:
                    self.root.after(0, lambda: self.gold_label.config(text="未检测到"))

            except Exception as e:
                self.root.after(0, lambda: self.log(f"监控错误: {e}"))
                break

            time.sleep(0.5)  # 每0.5秒检查一次

    def set_quick_amount(self, amount):
        """设置快捷金币数量"""
        self.gold_entry.delete(0, tk.END)
        self.gold_entry.insert(0, str(amount))

    def modify_gold(self):
        """修改金币"""
        if not self.is_connected or not self.gold_address:
            messagebox.showerror("错误", "请先连接游戏并等待检测到金币地址")
            return

        try:
            new_gold = int(self.gold_entry.get())
            if new_gold < 0:
                messagebox.showerror("错误", "金币数量不能为负数")
                return

            # 修改金币
            self.pm.write_int(self.gold_address, new_gold)

            # 验证修改
            time.sleep(0.1)
            actual_gold = self.pm.read_int(self.gold_address)

            if actual_gold == new_gold:
                self.log(f"成功！金币已修改为: {new_gold:,}")
                messagebox.showinfo("成功", f"金币已成功修改为: {new_gold:,}")
            else:
                self.log("写入失败！可能被游戏保护机制阻止")
                messagebox.showerror("失败", "金币修改失败，可能被游戏保护机制阻止")

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
        except Exception as e:
            self.log(f"修改金币时发生错误: {e}")
            messagebox.showerror("错误", f"修改金币时发生错误:\n{e}")

def modify_gold():
    """保留原有的命令行版本作为备用"""
    print("--- 《The Spell Brigade》金币修改器 (命令行版本) ---")
    print("提示：请先启动游戏，并以管理员身份运行此脚本！")

    try:
        # 1. 附加到游戏进程
        print(f"[*] 正在查找游戏进程: {PROCESS_NAME}...")
        pm = pymem.Pymem(PROCESS_NAME)
        print(f"[+] 成功附加到进程！进程ID: {pm.process_id}")
    except pymem.exception.ProcessNotFound:
        print(f"[-] 错误: 未找到游戏进程 '{PROCESS_NAME}'。")
        print("[-] 请先启动游戏，然后再运行此脚本。")
        return

    try:
        # 2. 获取 GameAssembly.dll 模块的基址
        module_base = pymem.process.module_from_name(pm.process_handle, "GameAssembly.dll").lpBaseOfDll
        print(f"[+] GameAssembly.dll 模块基址: {hex(module_base)}")

        # 3. 定义从你CE分析中得到的基址和偏移
        static_base_offset = 0x3858F00
        offsets = [0x20, 0xC0, 0x10, 0xB8, 0x0, 0x38]

        print("[*] 正在解析金币地址...")
        print(f"    基址: GameAssembly.dll + {hex(static_base_offset)}")
        print(f"    偏移: {[hex(o) for o in offsets]}")

        # 我们将持续监控和修改
        while True:
            # 4. 计算最终地址
            gold_address = find_pointer_address(pm, module_base + static_base_offset, offsets)

            if gold_address:
                try:
                    current_gold = pm.read_int(gold_address)
                    print(f"\r[*] 成功定位！当前金币: {current_gold} | 地址: {hex(gold_address)}  ", end="")

                    # 这里可以加入修改逻辑，比如按键修改
                    # 为简单起见，我们只在第一次定位成功后询问一次
                    new_gold_str = input("\n[?] 请输入你想要修改的金币数量 (输入 'q' 退出): ")
                    if new_gold_str.lower() == 'q':
                        break

                    new_gold = int(new_gold_str)
                    pm.write_int(gold_address, new_gold)

                    # 验证
                    time.sleep(0.1)
                    if pm.read_int(gold_address) == new_gold:
                        print(f"[!] 成功！金币已修改为: {new_gold}")
                        break # 修改成功后退出循环
                    else:
                        print("[-] 写入失败！")

                except (ValueError, TypeError):
                    print("\n[-] 无效输入，请输入一个纯数字。")
                except Exception as e:
                    print(f"\n[-] 读写内存时发生错误: {e}")
                    break
            else:
                print("\r[-] 正在等待玩家进入游戏... (无法解析指针)", end="")
                time.sleep(1) # 如果找不到地址，等1秒再试

    except Exception as e:
        print(f"\n[-] 发生未知错误: {e}")

def main():
    """主程序入口"""
    # 检查命令行参数，如果有 --cli 参数则使用命令行版本
    if len(sys.argv) > 1 and sys.argv[1] == '--cli':
        modify_gold()
        input("\n修改完成，按回车键退出...")
    else:
        # 启动GUI版本
        root = tk.Tk()
        app = SpellBrigadeCheatGUI(root)

        # 设置窗口关闭事件
        def on_closing():
            if app.is_connected:
                app.disconnect_game()
            root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        try:
            root.mainloop()
        except KeyboardInterrupt:
            on_closing()

if __name__ == "__main__":
    main()

