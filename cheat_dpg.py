import dearpygui.dearpygui as dpg
import pymem
import pymem.process
import threading
import time
from queue import Queue

# --- 配置区 ---
PROCESS_NAME = "TheSpellBrigade.exe"
# ----------------

def find_pointer_address(pm, base, offsets):
    """根据基址和多级偏移计算最终地址 (此函数无需修改)"""
    try:
        addr = pm.read_longlong(base)
        for offset in offsets[:-1]:
            if addr == 0: return None
            addr = pm.read_longlong(addr + offset)
        if addr == 0: return None
        return addr + offsets[-1]
    except Exception:
        return None

class ModifierApp:
    def __init__(self):
        # --- 状态变量 ---
        self.pm = None
        self.is_connected = False
        self.monitoring = False
        self.gold_address = None
        self.module_base = 0
        self.update_queue = Queue()  # 线程安全的队列，用于UI更新

        # --- 配置 ---
        self.static_base_offset = 0x3858F00
        self.offsets = [0x20, 0xC0, 0x10, 0xB8, 0x0, 0x38]
        self.quick_amounts = [1000, 5000, 10000, 50000, 100000, 999999]

        # --- DPG 标签 ---
        self.tags = {
            "status_label": "status_label",
            "connect_btn": "connect_btn",
            "gold_label": "gold_label",
            "gold_input": "gold_input",
            "modify_btn": "modify_btn",
            "log_text": "log_text",
            "primary_window": "primary_window"
        }
        for i, amount in enumerate(self.quick_amounts):
            self.tags[f"quick_btn_{i}"] = f"quick_btn_{i}"

    def _log(self, message):
        """将日志消息放入队列，由主线程处理"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.update_queue.put(("log", log_message))

    def _process_queue(self):
        """在主循环中处理队列中的UI更新任务"""
        while not self.update_queue.empty():
            command, data = self.update_queue.get_nowait()
            if command == "log":
                current_log = dpg.get_value(self.tags["log_text"])
                new_log = f"{current_log}\n{data}" if current_log else data
                dpg.set_value(self.tags["log_text"], new_log)
                # 自动滚动到底部
                dpg.set_y_scroll(self.tags["log_text"], -1.0)
            elif command == "update_gold":
                dpg.set_value(self.tags["gold_label"], f"{data:,}")
            elif command == "update_status":
                status, color = data
                dpg.set_value(self.tags["status_label"], status)
                dpg.configure_item(self.tags["status_label"], color=color)
            elif command == "update_ui_state":
                is_connected = data
                dpg.configure_item(self.tags["connect_btn"], label="断开连接" if is_connected else "连接游戏")
                dpg.configure_item(self.tags["modify_btn"], enabled=is_connected)
                for i in range(len(self.quick_amounts)):
                    dpg.configure_item(self.tags[f"quick_btn_{i}"], enabled=is_connected)


    def connect_game_callback(self):
        """连接/断开按钮的回调函数"""
        if self.is_connected:
            self.monitoring = False
            self.is_connected = False
            self.pm = None
            self.update_queue.put(("update_status", ("未连接", (255, 68, 68))))
            self.update_queue.put(("update_ui_state", False))
            self.update_queue.put(("update_gold", "0"))
            self._log("已断开游戏连接")
            return

        try:
            self._log(f"正在查找游戏进程: {PROCESS_NAME}...")
            self.pm = pymem.Pymem(PROCESS_NAME)
            self._log(f"成功附加到进程！进程ID: {self.pm.process_id}")

            self.module_base = pymem.process.module_from_name(self.pm.process_handle, "GameAssembly.dll").lpBaseOfDll
            self._log(f"GameAssembly.dll 模块基址: {hex(self.module_base)}")

            self.is_connected = True
            self.update_queue.put(("update_status", ("已连接", (68, 255, 68))))
            self.update_queue.put(("update_ui_state", True))
            
            # 启动监控线程
            self.monitoring = True
            threading.Thread(target=self.monitor_gold_thread, daemon=True).start()

        except pymem.exception.ProcessNotFound:
            self._log(f"错误: 未找到游戏进程 '{PROCESS_NAME}'")
        except Exception as e:
            self._log(f"连接失败: {e}")

    def monitor_gold_thread(self):
        """后台线程，持续监控金币数量"""
        while self.monitoring:
            if not self.pm: break
            
            try:
                addr = find_pointer_address(self.pm, self.module_base + self.static_base_offset, self.offsets)
                if addr:
                    self.gold_address = addr
                    gold_value = self.pm.read_int(addr)
                    self.update_queue.put(("update_gold", gold_value))
                else:
                    self.gold_address = None
                    self.update_queue.put(("update_gold", "未检测到"))
            except Exception:
                # 进程可能已关闭
                self.monitoring = False
                self.connect_game_callback() # 触发断开逻辑
                self._log("游戏进程已关闭或失去连接")
                break
            time.sleep(0.5)

    def quick_set_callback(self, sender, app_data, user_data):
        """快捷设置按钮的回调"""
        dpg.set_value(self.tags["gold_input"], user_data)
        self.modify_gold_callback()

    def modify_gold_callback(self):
        """修改金币按钮的回调"""
        if not self.is_connected or not self.gold_address:
            self._log("修改失败：请先连接游戏并等待检测到金币地址")
            return
        
        try:
            new_gold = dpg.get_value(self.tags["gold_input"])
            if new_gold < 0:
                self._log("修改失败：金币数量不能为负数")
                return

            self.pm.write_int(self.gold_address, new_gold)
            time.sleep(0.05) # 短暂延时以待游戏反应
            
            # 验证
            actual_gold = self.pm.read_int(self.gold_address)
            if actual_gold == new_gold:
                self._log(f"成功！金币已修改为: {new_gold:,}")
            else:
                self._log("写入失败！可能被游戏保护机制阻止")

        except Exception as e:
            self._log(f"修改金币时发生错误: {e}")

    def create_gui(self):
        """创建所有Dear PyGui界面元素"""
        with dpg.window(tag=self.tags["primary_window"]):
            dpg.add_text("The Spell Brigade 金币修改器", color=(255, 255, 0))
            dpg.add_separator()

            with dpg.group(horizontal=True):
                dpg.add_text("游戏状态:")
                dpg.add_text("未连接", tag=self.tags["status_label"], color=(255, 68, 68))

            dpg.add_button(label="连接游戏", tag=self.tags["connect_btn"], callback=self.connect_game_callback)
            dpg.add_separator()

            with dpg.group(horizontal=True):
                dpg.add_text("当前金币:")
                dpg.add_text("0", tag=self.tags["gold_label"], color=(255, 221, 68))

            with dpg.group(horizontal=True):
                dpg.add_input_int(tag=self.tags["gold_input"], label="修改数量", default_value=999999, width=150)
                dpg.add_button(label="修改金币", tag=self.tags["modify_btn"], callback=self.modify_gold_callback, enabled=False)
            
            dpg.add_text("快捷设置:")
            with dpg.group(horizontal=True):
                for i, amount in enumerate(self.quick_amounts):
                    dpg.add_button(label=f"{amount:,}", tag=self.tags[f"quick_btn_{i}"],
                                   callback=self.quick_set_callback, user_data=amount, enabled=False)

            dpg.add_separator()
            dpg.add_text("操作日志:")
            dpg.add_input_text(tag=self.tags["log_text"], multiline=True, readonly=True, width=-1, height=-1)

    def run(self):
        """启动应用"""
        dpg.create_context()

        self.create_gui()

        dpg.create_viewport(title='The Spell Brigade 修改器', width=600, height=500)
        dpg.setup_dearpygui()
        dpg.show_viewport()
        dpg.set_primary_window(self.tags["primary_window"], True)
        
        self._log("程序启动完成")
        self._log("提示：请先启动游戏，然后点击'连接游戏'按钮")

        while dpg.is_dearpygui_running():
            self._process_queue() # 在主循环中安全地更新UI
            dpg.render_dearpygui_frame()

        self.monitoring = False # 确保线程退出
        dpg.destroy_context()

if __name__ == "__main__":
    app = ModifierApp()
    app.run()
